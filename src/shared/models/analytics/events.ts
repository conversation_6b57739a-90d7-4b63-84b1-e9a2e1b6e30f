import { captureException } from '@sentry/react';
import { attach, createEffect } from 'effector';
import posthog from 'posthog-js';
import ReactGA from 'react-ga4';
import { googleAnalyticsId, isProd, postHogHost, postHogKey } from 'shared/lib';
import { authModel } from 'shared/models/auth';

export const events = {
  initPosthog: createEffect(() => {
    if (!isProd) return;

    if (!postHogKey || !postHogHost) {
      captureException(
        'Posthog is not initialized because project id or host is not provided',
        {
          tags: { source: 'handleUnknownError' },
        },
      );
      return;
    }

    posthog.init(postHogKey, {
      api_host: postHogHost,
      capture_exceptions: true,
      debug: !isProd,
      person_profiles: 'always',
      session_idle_timeout_seconds: 180, // 3 minutes
    });

    // Make PostHog available globally as PH to prevent "PH is not defined" errors
    if (typeof window !== 'undefined') {
      window.PH = posthog;
    }
  }),

  initGoogleAnalytics: createEffect(() => {
    if (!googleAnalyticsId) return;

    ReactGA.initialize(googleAnalyticsId, {
      testMode: !isProd,
      gtagOptions: {
        debug_mode: !isProd,
      },
    });
  }),
  setPosthogUser: attach({
    source: authModel.$user,
    effect(user) {
      try {
        if (!user || !posthog) {
          return;
        }

        posthog.identify(user.id.toString(), {
          userId: user.id ?? 'No User ID',
          email: user.email ?? 'No User Email',
          name: !(!user.firstName && !user.lastName)
            ? `${user.firstName ?? ''} ${user.lastName ?? ''}`.trimStart()
            : 'No User Name',
        });
      } catch (error) {
        console.warn('PostHog setPosthogUser error:', error);
      }
    },
  }),

  setGoogleAnalyticsUser: attach({
    source: authModel.$user,
    effect(user) {
      if (!user || !googleAnalyticsId) {
        return;
      }

      ReactGA.set({
        user_id: user.id.toString(),
        custom_map: {
          dimension1: user.email ?? 'No User Email',
          dimension2: !(!user.firstName && !user.lastName)
            ? `${user.firstName ?? ''} ${user.lastName ?? ''}`.trimStart()
            : 'No User Name',
        },
      });
    },
  }),
};
