import type { MeUserFragment } from 'api/core/generated';
import { isDevelopment, POSTHOG_HOST, POSTHOG_KEY, region } from 'environment';
import posthog from 'posthog-js';

type RegisterPropertyInPostHogParams = {
  name: string;
  value: string;
};

const POSTHOG_IDENTIFIER_PROPERTY_NAME = 'identifier';

export const isPostHogEnabled = Boolean(POSTHOG_KEY && POSTHOG_HOST);

export const initializePostHog = () => {
  if (!isPostHogEnabled) {
    return;
  }

  posthog.init(POSTHOG_KEY, {
    api_host: POSTHOG_HOST,
    capture_exceptions: true,
    debug: isDevelopment,
    person_profiles: 'always',
    session_idle_timeout_seconds: 180, // 3 minutes
    capture_pageview: true,
    capture_pageleave: true,
    session_recording: {
      maskCapturedNetworkRequestFn: (request) => {
        // Return the request unchanged to avoid any redaction
        return request;
      },
    },
  });

  // Make PostHog available globally as PH to prevent "PH is not defined" errors
  if (typeof window !== 'undefined') {
    window.PH = posthog;
  }
};

export const registerPropertyInPostHog = ({
  name,
  value,
}: RegisterPropertyInPostHogParams) => {
  try {
    if (isPostHogEnabled && posthog) {
      posthog.register({ [name]: value });
    }
  } catch (error) {
    console.warn('PostHog registerProperty error:', error);
  }
};

export const registerIdentifierPropertyInPostHog = (identifier: string) => {
  registerPropertyInPostHog({
    name: POSTHOG_IDENTIFIER_PROPERTY_NAME,
    value: identifier,
  });
};

type IdentifyUserInPosthogParams = {
  user: Nullable<MeUserFragment>;
  hashIdentifier: Nullable<string>;
};

export const identifyUserInPostHog = ({
  user,
  hashIdentifier,
}: IdentifyUserInPosthogParams) => {
  try {
    if (!isPostHogEnabled || !posthog) {
      return;
    }

    if (!user && !hashIdentifier) {
      return;
    }

    if (!user && hashIdentifier) {
      registerIdentifierPropertyInPostHog(hashIdentifier);
      return;
    }

    const userId = `${region}-${user?.id}`;

    posthog.identify(userId, {
      userId: user?.id ? userId : `${region}-No User ID`,
      email: user?.email || 'No User Email',
      name: !(!user?.profile?.first_name && !user?.profile?.last_name)
        ? `${user.profile?.first_name ?? ''} ${
            user.profile?.last_name ?? ''
          }`.trimStart()
        : 'No User Name',
    });
  } catch (error) {
    console.warn('PostHog identifyUser error:', error);
  }
};

// Safe PostHog access utility to prevent "PH is not defined" errors
export const safePostHogCall = (fn: (ph: typeof posthog) => void) => {
  try {
    if (isPostHogEnabled && posthog) {
      fn(posthog);
    } else if (
      typeof window !== 'undefined' &&
      window.PH &&
      typeof window.PH === 'object'
    ) {
      fn(window.PH as typeof posthog);
    }
  } catch (error) {
    console.warn('PostHog safe call error:', error);
  }
};

// Export posthog instance for direct access if needed
export { posthog };
