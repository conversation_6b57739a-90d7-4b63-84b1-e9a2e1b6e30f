import { userApi } from '@entities/user';
import { useMount } from 'react-use';
import {
  identifyUserInPostHog,
  isPostHogEnabled,
} from '@/services/posthog-service';

export { isPostHogEnabled };

export const usePosthogAnalytics = () => {
  const { data: user } = userApi.useSuspenseUserQuery(undefined, {
    select: (data) => data?.me ?? null,
  });

  useMount(() => {
    // PostHog is already initialized in the service, just identify the user
    identifyUserInPostHog({ user, hashIdentifier: null });
  });
};
